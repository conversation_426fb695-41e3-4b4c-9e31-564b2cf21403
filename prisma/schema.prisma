// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Product {
  id          String @id @default(uuid())
  name        String
  price       Float
  image       String?
  slug        String   @unique
  description String?
  category    Category @relation(fields: [categoryId], references: [id])
  categoryId  String
}

model Category {
  id        String   @id @default(uuid())
  name      String
  slug      String   @unique
  products  Product[]
}