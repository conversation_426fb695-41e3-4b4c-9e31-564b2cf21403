import { prisma } from "../app/lib/prisma";

async function main() {
    await prisma.category.deleteMany()
    await prisma.product.deleteMany()

    const electronicsCategory = await prisma.category.create({
        data: {
            name: "Electronics",
            slug: "electronics"
        }
    })

    const clothingCategory = await prisma.category.create({
        data: {
            name: "Clothing",
            slug: "clothing"
        }
    })

    const homeCategory = await prisma.category.create({
        data: {
            name: "Home",
            slug: "home"
        }
    })

    const products = [
        {
            name: "Laptop Pro X",
            price: 1299.99,
            image: "https://images.unsplash.com/photo-1662569081210-6e8b240def2e?q=80&w=3870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            slug: "laptop-pro-x",
            description: "Potente laptop con procesador de última generación, 16GB RAM y 512GB SSD.",
            categoryId: electronicsCategory.id
        },
        {
            name: "Mouse Ergonómico",
            price: 49.99,
            image: "https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?q=80&w=3867&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            slug: "mouse-ergonomic",
            description: "Mouse ergonómico inalámbrico con diseño vertical para reducir la fatiga.",
            categoryId: electronicsCategory.id
        },
        {
            name: "Teclado Mecánico RGB",
            price: 89.99,
            image: "https://images.unsplash.com/photo-1706819512229-3fbf5ac1296a?q=80&w=3474&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            slug: "mechanical-keyboard-rgb",
            description: "Teclado mecánico con retroiluminación RGB personalizable y switches Cherry MX.",
            categoryId: electronicsCategory.id
        },
        {
            name: "Camiseta de Fútbol",
            price: 29.99,
            image: "https://images.unsplash.com/photo-1662569081210-6e8b240def2e?q=80&w=3870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            slug: "football-jersey",
            description: "Camiseta de fútbol de alta calidad con detalles personalizados.",
            categoryId: clothingCategory.id
        },
        {
            name: "Cama de Lujo",
            price: 1999.99,
            image: "https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?q=80&w=3867&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            slug: "luxury-bed",
            description: "Cama de lujo con diseño moderno y funcionalidad premium.",
            categoryId: homeCategory.id
        },
        {
            name: "Sofá de Lujo",
            price: 999.99,
            image: "https://images.unsplash.com/photo-1706819512229-3fbf5ac1296a?q=80&w=3474&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            slug: "luxury-sofa",
            description: "Sofá de lujo con diseño moderno y funcionalidad premium.",
            categoryId: homeCategory.id
        }
    ]

    for (const product of products) {
        await prisma.product.create({
            data: product
        })
    }
}

main()
    .then(async () => {
        console.log("Seed completed")
        await prisma.$disconnect()
    })
    .catch(async (e) => {
        console.error(e)
        await prisma.$disconnect()
        process.exit(1)
    })